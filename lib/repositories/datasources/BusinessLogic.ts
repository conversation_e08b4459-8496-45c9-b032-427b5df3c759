import {
  <PERSON>source,
  DatasourceCreateInput,
  DatasourceUpdateInput,
  DatasourceQueryParams,
  DatasourceBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { DatasourceDBRepository } from "./DBRepository"
import {
  DatasourceVectorDB,
  DatasourceVectorDBRepository,
} from "./VectorDBRepository"
import { SessionContext } from "../auth/types"

export class DatasourceBusinessLogic
  implements DatasourceBusinessLogicInterface {
  constructor(
    private db: DatasourceDBRepository,
    private vectordb: DatasourceVectorDBRepository,
  ) { }

  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim().length === 0) {
      throw createError("Invalid ID provided", "INVALID_ID")
    }
  }

  private trimCreateInput(data: DatasourceCreateInput): DatasourceCreateInput {
    return {
      ...data,
      name: data.name.trim(),
      type: data.type.trim(),
    }
  }

  private trimUpdateInput(data: DatasourceUpdateInput): DatasourceUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
      type: data.type?.trim(),
      url: data.url?.trim(),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<Datasource | null> {
    this.validateId(id)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: DatasourceQueryParams,
    context: SessionContext,
  ): Promise<{ items: Datasource[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }
    return this.db.getAll(paramsWithContext)
  }

  async create(
    data: DatasourceCreateInput,
    context: SessionContext,
  ): Promise<Datasource> {
    const trimmedData = this.trimCreateInput(data)

    // Add context information to the data
    const dataWithContext = {
      ...trimmedData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    // Check for duplicate name within the same context
    // const contextFilters = this.buildContextFilters(context)
    // const existing = await this.db.getCount({
    //   filters: [{ field: "name", value: trimmedData.name }, ...contextFilters],
    // })

    // if (existing.total > 0) {
    //   throw createError(
    //     "Datasource with this name already exists",
    //     "DUPLICATE_NAME",
    //   )
    // }

    const result = await this.db.create(dataWithContext)

    // Add to vector database
    let vectorContent = `${result.name} ${result.type}`

    if (result.type === "TOOL") {
      // For TOOL type, use query and tool information for vector content
      vectorContent += ` ${result.query || ""} ${result.toolCallConfig?.toolName || ""} ${result.toolCallConfig?.description || ""}`
    } else {
      // For other types, use existing logic
      vectorContent += ` ${result.content || ""}`
    }

    await this.vectordb.create(
      {
        id: result.id,
        content: vectorContent,
      },
      context,
    )

    return result
  }

  async update(
    id: string,
    data: DatasourceUpdateInput,
    context: SessionContext,
  ): Promise<Datasource | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if datasource exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Datasource not found", "NOT_FOUND")
    }

    const existingDatasource = existingResult.items[0]

    if (data.name && data.name.trim() !== existingDatasource.name) {
      const duplicates = await this.db.getAll({
        filters: [
          { field: "name", value: data.name.trim() },
          ...contextFilters,
        ],
      })
      if (duplicates.items.some((d) => d.id !== id)) {
        throw createError(
          "Another Datasource with this name exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }

    const result = await this.db.update(id, trimmedData)

    // Update vector database if result exists
    if (result) {
      let vectorContent = `${result.name} ${result.type}`

      if (result.type === "TOOL") {
        // For TOOL type, use query and tool information for vector content
        vectorContent += ` ${result.query || ""} ${result.toolCallConfig?.toolName || ""} ${result.toolCallConfig?.description || ""}`
      } else {
        // For other types, use existing logic
        vectorContent += ` ${result.content || ""}`
      }

      await this.vectordb.update(
        id,
        {
          id: result.id,
          content: vectorContent,
        },
        context,
      )
    }

    return result
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if datasource exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Datasource not found", "NOT_FOUND")
    }

    const success = await this.db.delete(id, hardDelete)

    // Remove from vector database if deletion was successful
    if (success) {
      try {
        await this.vectordb.delete(id, context)
      } catch (error) {
        console.error("Failed to delete from vector database:", error)
        // Continue even if vector deletion fails
      }
    }

    return success
  }

  async searchMatchingQuery(
    query: string,
    context: SessionContext,
    limit = 5,
  ): Promise<Datasource[]> {
    if (!query || !query.trim()) {
      return []
    }

    const searchTerm = query.trim()
    const vectorResults = await this.vectordb.search(searchTerm, limit, context)

    // Filter vector results to only include datasources that belong to the current context
    const contextFilters = this.buildContextFilters(context)
    const filteredResults = []

    for (const vectorResult of vectorResults) {
      const datasourceResult = await this.db.getAll({
        filters: [{ field: "id", value: vectorResult.id }, ...contextFilters],
      })

      if (datasourceResult.items.length > 0) {
        filteredResults.push(datasourceResult.items[0])
      }
    }

    return filteredResults
  }
}
