import { BaseEntity } from "./base"

// Integration Types
export type IntegrationType = "google" | "microsoft" | "slack" | "discord"
export type IntegrationStatus = "connected" | "disconnected" | "error" | "pending"

// Base Integration Interface
export interface Integration extends BaseEntity {
  userId: string
  type: IntegrationType
  status: IntegrationStatus
  name: string
  description?: string
  
  // Encrypted OAuth data
  encryptedAccessToken?: string
  encryptedRefreshToken?: string
  tokenExpiresAt?: Date
  
  // Integration-specific metadata
  metadata?: Record<string, any>
  
  // Connection info
  connectedAt?: Date
  lastSyncAt?: Date
  
  // Error tracking
  lastError?: string
  errorCount?: number
}

// Google OAuth specific types
export interface GoogleOAuthTokens {
  access_token: string
  refresh_token?: string
  expires_in: number
  token_type: string
  scope: string
}

export interface GoogleUserInfo {
  id: string
  email: string
  name: string
  picture?: string
  given_name?: string
  family_name?: string
}

export interface GoogleOAuthMetadata {
  userInfo: GoogleUserInfo
  scopes: string[]
  grantedPermissions: string[]
}

// API Request/Response Types
export interface IntegrationCreateInput {
  type: IntegrationType
  name: string
  description?: string
}

export interface IntegrationUpdateInput {
  name?: string
  description?: string
  status?: IntegrationStatus
}

export interface IntegrationQueryParams {
  page?: number
  pageSize?: number
  search?: string
  type?: IntegrationType
  status?: IntegrationStatus
  userId?: string
}

// OAuth Flow Types
export interface OAuthInitiateRequest {
  type: IntegrationType
  redirectUri?: string
  scopes?: string[]
}

export interface OAuthInitiateResponse {
  authUrl: string
  state: string
}

export interface OAuthCallbackRequest {
  code: string
  state: string
  type: IntegrationType
}

export interface OAuthCallbackResponse {
  integration: Integration
  success: boolean
  message?: string
}

// Integration Connection Status
export interface IntegrationConnectionStatus {
  isConnected: boolean
  lastConnected?: Date
  lastError?: string
  tokenValid: boolean
  needsReauth: boolean
}

// Google OAuth Configuration
export interface GoogleOAuthConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scopes: string[]
}

// Integration Service Response Types
export interface IntegrationTestResponse {
  success: boolean
  message: string
  data?: any
}

export interface IntegrationSyncResponse {
  success: boolean
  syncedAt: Date
  itemsProcessed?: number
  errors?: string[]
}
