"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Plus, Trash2 } from "lucide-react"
import { DatasourceFormData } from "./DatasourceFormBase"

interface ToolDatasourceFieldsProps {
  formData: DatasourceFormData
  onFormDataChange: (data: DatasourceFormData) => void
}

export default function ToolDatasourceFields({
  formData,
  onFormDataChange,
}: ToolDatasourceFieldsProps) {
  const updateFormData = (updates: Partial<DatasourceFormData>) => {
    onFormDataChange({ ...formData, ...updates })
  }

  const updateToolConfig = (updates: Partial<NonNullable<DatasourceFormData['toolCallConfig']>>) => {
    updateFormData({
      toolCallConfig: {
        ...formData.toolCallConfig!,
        ...updates,
      },
    })
  }

  const addParameter = () => {
    updateToolConfig({
      parameters: [
        ...(formData.toolCallConfig?.parameters || []),
        {
          name: "",
          type: "string",
          description: "",
          required: false,
        },
      ],
    })
  }

  const removeParameter = (index: number) => {
    updateToolConfig({
      parameters: formData.toolCallConfig?.parameters?.filter((_, i) => i !== index) || [],
    })
  }

  const updateParameter = (index: number, updates: Partial<NonNullable<DatasourceFormData['toolCallConfig']>['parameters'][0]>) => {
    const newParams = [...(formData.toolCallConfig?.parameters || [])]
    newParams[index] = { ...newParams[index], ...updates }
    updateToolConfig({ parameters: newParams })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tool Configuration</CardTitle>
        <CardDescription>
          Configure the tool call parameters and query for matching
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Query Field */}
        <div className="space-y-2">
          <Label htmlFor="query">Query for Matching</Label>
          <Textarea
            id="query"
            value={formData.query || ""}
            onChange={(e) => updateFormData({ query: e.target.value })}
            placeholder="Enter the query that will be used to match this tool in vector database..."
            className="min-h-20"
            maxLength={1000}
            required
          />
          <p className="text-sm text-gray-500">
            {(formData.query || "").length}/1,000 characters
          </p>
        </div>

        {/* Tool Name */}
        <div className="space-y-2">
          <Label htmlFor="toolName">Tool Name</Label>
          <Input
            id="toolName"
            value={formData.toolCallConfig?.toolName || ""}
            onChange={(e) => updateToolConfig({ toolName: e.target.value })}
            placeholder="e.g., get_weather, search_database"
            required
          />
        </div>

        {/* Tool Description */}
        <div className="space-y-2">
          <Label htmlFor="toolDescription">Tool Description</Label>
          <Textarea
            id="toolDescription"
            value={formData.toolCallConfig?.description || ""}
            onChange={(e) => updateToolConfig({ description: e.target.value })}
            placeholder="Describe what this tool does and when to use it..."
            className="min-h-20"
            required
          />
        </div>

        {/* Tool Parameters */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Tool Parameters</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addParameter}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Parameter
            </Button>
          </div>

          {(formData.toolCallConfig?.parameters || []).map((param, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Parameter {index + 1}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeParameter(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label>Name</Label>
                  <Input
                    value={param.name}
                    onChange={(e) => updateParameter(index, { name: e.target.value })}
                    placeholder="parameter_name"
                  />
                </div>
                <div>
                  <Label>Type</Label>
                  <Select
                    value={param.type}
                    onValueChange={(value) => updateParameter(index, { type: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="string">String</SelectItem>
                      <SelectItem value="number">Number</SelectItem>
                      <SelectItem value="boolean">Boolean</SelectItem>
                      <SelectItem value="object">Object</SelectItem>
                      <SelectItem value="array">Array</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  value={param.description}
                  onChange={(e) => updateParameter(index, { description: e.target.value })}
                  placeholder="Describe this parameter..."
                  className="min-h-16"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`required-${index}`}
                  checked={param.required}
                  onChange={(e) => updateParameter(index, { required: e.target.checked })}
                />
                <Label htmlFor={`required-${index}`}>Required parameter</Label>
              </div>
            </div>
          ))}

          {(formData.toolCallConfig?.parameters || []).length === 0 && (
            <p className="text-sm text-gray-500 text-center py-4">
              No parameters defined. Click "Add Parameter" to add tool parameters.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function validateToolDatasource(formData: DatasourceFormData): boolean {
  return !!(
    formData.name.trim() &&
    formData.query?.trim() &&
    formData.toolCallConfig?.toolName?.trim() &&
    formData.toolCallConfig?.description?.trim()
  )
}
