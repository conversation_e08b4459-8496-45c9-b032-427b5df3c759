"use client"

import { useEffect, useState } from "react"
import { Datasource, ToolCallConfig } from "@/lib/repositories/datasources/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import DatasourceFormBase, { DatasourceFormData } from "./DatasourceFormBase"
import TextDatasourceFields, { validateTextDatasource } from "./TextDatasourceFields"
import ToolDatasourceFields, { validateToolDatasource } from "./ToolDatasourceFields"

interface DatasourceFormProps {
  initialDatasource?: Datasource | null
  onSave: (data: {
    name: string
    type: string
    content?: string
    isActive: boolean
    toolCallConfig?: ToolCallConfig
    query?: string
  }) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
}

export default function DatasourceForm({
  initialDatasource,
  onSave,
  onCancel,
  isSubmitting = false,
  submitButtonText,
  title,
  description,
}: DatasourceFormProps) {
  const { t } = useLocalization("datasource", locales)

  const [formData, setFormData] = useState<DatasourceFormData>(initialDatasource ? {
    name: initialDatasource.name || "",
    type: initialDatasource.type || "TEXT",
    content: initialDatasource.content || "",
    isActive: initialDatasource.isActive ?? true,
    query: initialDatasource.query || "",
    toolCallConfig: initialDatasource.toolCallConfig || {
      toolName: "",
      description: "",
      parameters: [],
    },
  } : {
    name: "",
    type: "TEXT",
    content: "",
    isActive: true,
    query: "",
    toolCallConfig: {
      toolName: "",
      description: "",
      parameters: [],
    },
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) {
      alert(t("form.validation.name_required"))
      return
    }

    if (formData.type === "TEXT" && !formData.content?.trim()) {
      alert(t("form.validation.content_required"))
      return
    }

    if (formData.type === "TOOL") {
      if (!formData.query?.trim()) {
        alert("Query is required for TOOL type")
        return
      }
      if (!formData.toolCallConfig?.toolName?.trim()) {
        alert("Tool name is required for TOOL type")
        return
      }
      if (!formData.toolCallConfig?.description?.trim()) {
        alert("Tool description is required for TOOL type")
        return
      }
    }

    try {
      await onSave({
        name: formData.name.trim(),
        type: formData.type,
        content: formData.type === "TEXT" ? formData.content?.trim() : undefined,
        isActive: formData.isActive,
        query: formData.type === "TOOL" ? formData.query?.trim() : undefined,
        toolCallConfig: formData.type === "TOOL" ? formData.toolCallConfig : undefined,
      })
    } catch (error) {
      console.error("Error saving datasource:", error)
      alert(t("form.validation.save_failed"))
    }
  }

  const getValidationFunction = () => {
    switch (formData.type) {
      case "TEXT":
        return validateTextDatasource
      case "TOOL":
        return validateToolDatasource
      default:
        return () => false
    }
  }

  const isFormValid = getValidationFunction()(formData)

  const renderTypeSpecificFields = () => {
    switch (formData.type) {
      case "TEXT":
        return (
          <TextDatasourceFields
            formData={formData}
            onFormDataChange={setFormData}
          />
        )
      case "TOOL":
        return (
          <ToolDatasourceFields
            formData={formData}
            onFormDataChange={setFormData}
          />
        )
      default:
        return null
    }
  }

  return (
    <DatasourceFormBase
      formData={formData}
      onFormDataChange={setFormData}
      onSubmit={handleSubmit}
      onCancel={onCancel}
      isSubmitting={isSubmitting}
      submitButtonText={submitButtonText}
      title={title}
      description={description}
      isFormValid={isFormValid}
    >
      {renderTypeSpecificFields()}
    </DatasourceFormBase>
  )
}