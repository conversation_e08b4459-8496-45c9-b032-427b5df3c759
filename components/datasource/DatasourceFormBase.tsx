"use client"

import { ReactNode } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Save,
  FileText,
  Globe,
  Database,
  Wrench,
} from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { ToolParameter } from "@/lib/repositories/datasources/interface"

export interface DatasourceFormData {
  name: string
  type: string
  content?: string
  isActive: boolean
  query?: string
  toolCallConfig?: {
    toolName: string
    description: string
    parameters: ToolParameter[]
  }
}

interface DatasourceFormBaseProps {
  formData: DatasourceFormData
  onFormDataChange: (data: DatasourceFormData) => void
  onSubmit: (e: React.FormEvent) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
  children: ReactNode // Type-specific fields
  isFormValid: boolean
}

export default function DatasourceFormBase({
  formData,
  onFormDataChange,
  onSubmit,
  onCancel,
  isSubmitting = false,
  submitButtonText,
  title,
  description,
  children,
  isFormValid,
}: DatasourceFormBaseProps) {
  const { t } = useLocalization("datasource", locales)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "TEXT":
        return <FileText className="h-5 w-5" />
      case "TOOL":
        return <Wrench className="h-5 w-5" />
      default:
        return <Database className="h-5 w-5" />
    }
  }

  const updateFormData = (updates: Partial<DatasourceFormData>) => {
    onFormDataChange({ ...formData, ...updates })
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>

      <form onSubmit={onSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTypeIcon(formData.type)}
              {t("form.section.details_title")}
            </CardTitle>
            <CardDescription>
              {t("form.section.details_desc")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("form.fields.name.label")}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => updateFormData({ name: e.target.value })}
                placeholder="Enter datasource name"
                maxLength={100}
                required
              />
              <p className="text-sm text-gray-500">
                {formData.name.length}/100 characters
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">{t("form.fields.type.label")}</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => updateFormData({ type: value })}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t("form.fields.type.placeholder")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TEXT">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {t("form.fields.type.text")}
                    </div>
                  </SelectItem>
                  <SelectItem value="TOOL">
                    <div className="flex items-center gap-2">
                      <Wrench className="h-4 w-4" />
                      Tool Call
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Type-specific fields */}
        {children}

        {/* Submit */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t("form.buttons.cancel")}
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !isFormValid}
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {t("form.buttons.saving")}
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {submitButtonText ?? t("form.buttons.save")}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
