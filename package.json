{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,ts,jsx,tsx}\""}, "dependencies": {"@blocknote/core": "^0.37.0", "@blocknote/mantine": "^0.37.0", "@blocknote/react": "^0.37.0", "@floating-ui/react": "^0.27.16", "@hookform/resolvers": "^3.9.1", "@pinecone-database/pinecone": "^6.1.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tiptap/extension-code-block-lowlight": "^3.3.1", "@tiptap/extension-highlight": "^3.3.1", "@tiptap/extension-horizontal-rule": "^3.3.1", "@tiptap/extension-image": "^3.3.1", "@tiptap/extension-link": "^3.3.1", "@tiptap/extension-list": "^3.3.1", "@tiptap/extension-placeholder": "^3.3.1", "@tiptap/extension-subscript": "^3.3.1", "@tiptap/extension-superscript": "^3.3.1", "@tiptap/extension-table": "^3.3.1", "@tiptap/extension-table-cell": "^3.3.1", "@tiptap/extension-table-header": "^3.3.1", "@tiptap/extension-table-row": "^3.3.1", "@tiptap/extension-text-align": "^3.3.1", "@tiptap/extension-typography": "^3.3.1", "@tiptap/extensions": "^3.3.1", "@tiptap/pm": "^3.3.1", "@tiptap/react": "^3.3.1", "@tiptap/starter-kit": "^3.3.1", "@types/jsonwebtoken": "^9.0.10", "@types/pouchdb": "^6.4.2", "@types/pouchdb-find": "^7.3.3", "@upstash/redis": "^1.35.1", "amqplib": "^0.10.4", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "google-auth-library": "^10.3.0", "googleapis": "^160.0.0", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash.throttle": "^4.1.1", "lowlight": "^3.3.0", "lucide-react": "^0.454.0", "mongodb": "^6.17.0", "next": "15.2.4", "next-themes": "^0.4.4", "pouchdb": "^9.0.0", "pouchdb-find": "^9.0.0", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redis": "^5.8.2", "remark-gfm": "^4.0.1", "resend": "^6.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.1", "uuid": "^11.1.0", "vaul": "^0.9.6", "ws": "^8.18.3", "zod": "^3.25.76"}, "devDependencies": {"@types/amqplib": "^0.10.5", "@types/lodash.throttle": "^4.1.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "mongodb-memory-server": "^10.1.4", "postcss": "^8.5", "prettier": "^3.6.2", "sass": "^1.92.0", "tailwindcss": "^3.4.17", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}}