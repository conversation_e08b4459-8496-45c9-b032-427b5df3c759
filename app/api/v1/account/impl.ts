import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { AccountBusinessLogicInterface } from "@/lib/repositories/account/interface"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ERROR_CODES } from "@/app/api/error_codes"
import { UpdateAccountNameSchema } from "@/lib/validations/account"

// Get Account Information Implementation
export async function implHandleGetAccount(
  businessLogic: AccountBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const accountInfo = await businessLogic.getAccount(context)

    if (!accountInfo) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Account not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", accountInfo),
    }
  } catch (error: any) {
    console.error("Get account error:", error)

    if (error.code === "UNAUTHORIZED") {
      return {
        status: 401,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.UNAUTHORIZED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to get account information"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Update Account Name Implementation
export async function implHandleUpdateAccountName(
  data: any,
  businessLogic: AccountBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = UpdateAccountNameSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    // Update the account name using the business logic
    const success = await businessLogic.updateName(
      validationResult.data.name,
      context,
    )

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Account not found or update failed"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { success: true }),
    }
  } catch (error: any) {
    console.error("Update account name error:", error)

    if (error.code === "UNAUTHORIZED") {
      return {
        status: 401,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.UNAUTHORIZED],
        ),
      }
    }

    if (error.code === "INVALID_INPUT") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to update account name"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
