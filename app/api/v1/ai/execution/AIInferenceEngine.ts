export interface AIInferenceEngine {
  execute(
    conversationId: string,
    executionId: string,
    context: string,
    payload: {
      message: string;
      PREVIOUS_MESSAGES?: any[];
      data: any;
      toolResult?: {
        toolName: string;
        success: boolean;
        data?: any;
        error?: string;
      };
    },
    headers?: { key: string; value: string }[],
  ): Promise<{
    status: "success" | "failed"
    data?: any
    errors?: string[]
    errorCodes?: string[]
  }>
}
