import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { implHandleSendMessage } from "../../functions/messages/send/impl"
import { getExecutionIdAiExtras } from "../../functions/webhook/impl"
import { AiBusinessLogic } from "./AIBusinessLogic"
import { getAiServices } from "./shared"

export async function implHandleTryAnswer(
  conversationId: string,
  executionId: string,
  body: { message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const rules = await businessLogic.tryAnswer(
    conversationId,
    executionId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed TRY_ANSWER",
      rules,
    }),
  }
}

export async function implHandleReply(
  sessionId: string,
  executionId: string,
  body: { final_message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  try {
    const cs_ai_extras = await getExecutionIdAiExtras(executionId)
    if (!cs_ai_extras) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation context not found"],
          ["CONVERSATION_CONTEXT_NOT_FOUND"],
        ),
      }
    }

    await implHandleSendMessage(
      { conversationId: cs_ai_extras.msg_room_id, text: body.final_message },
      context,
      true,
    )
    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Executed REPLY",
        // context,
        // result
      }),
    }
  } catch (error) {
    console.error("❌ Error during AI workflow handling:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        ["INTERNAL_SERVER_ERROR"],
      ),
    }
  }
}

export async function implToolCall(
  sessionId: string,
  executionId: string,
  body: {
    tool_name: string
    tool_parameters: Record<string, any>
    datasource_id: string
    conversation_id: string
    original_message: string
  },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  try {
    console.log("🔧 Tool call received:", {
      toolName: body.tool_name,
      datasourceId: body.datasource_id,
      parameters: body.tool_parameters,
      conversationId: body.conversation_id,
    })

    // Get the datasource business logic from shared services
    const { aiBusinessLogic: aiServices } = getAiServices()
    const datasourceBusinessLogic = (aiServices as any).dataSourceBusinessLogic

    // Get the datasource by ID
    const datasource = await datasourceBusinessLogic.getById(
      body.datasource_id,
      context,
    )

    if (!datasource) {
      console.error("❌ Datasource not found:", body.datasource_id)
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Datasource not found"],
          ["DATASOURCE_NOT_FOUND"],
        ),
      }
    }

    // Check if datasource is TOOL type
    if (datasource.type !== "TOOL") {
      console.error("❌ Datasource is not TOOL type:", datasource.type)
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Datasource is not a TOOL type"],
          ["INVALID_DATASOURCE_TYPE"],
        ),
      }
    }

    // Validate tool configuration
    if (!datasource.toolCallConfig) {
      console.error("❌ Tool configuration missing for datasource:", body.datasource_id)
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Tool configuration missing"],
          ["TOOL_CONFIG_MISSING"],
        ),
      }
    }

    // Validate tool name matches
    if (datasource.toolCallConfig.toolName !== body.tool_name) {
      console.error("❌ Tool name mismatch:", {
        expected: datasource.toolCallConfig.toolName,
        received: body.tool_name,
      })
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Tool name mismatch"],
          ["TOOL_NAME_MISMATCH"],
        ),
      }
    }

    // Validate parameters
    const validationResult = validateToolParameters(
      body.tool_parameters,
      datasource.toolCallConfig.parameters,
    )

    if (!validationResult.isValid) {
      console.error("❌ Parameter validation failed:", validationResult.errors)
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.errors,
          ["PARAMETER_VALIDATION_FAILED"],
        ),
      }
    }

    console.log("✅ Tool call validation passed, executing tool:", body.tool_name)
    console.log("📋 Tool parameters:", body.tool_parameters)

    // Mock tool execution (replace with actual implementation later)
    const toolResult = await mockToolExecution(
      body.tool_name,
      body.tool_parameters,
      datasource.toolCallConfig,
    )

    console.log("🎯 Tool execution result:", toolResult)

    // Call TRY_ANSWER again with tool result
    const tryAnswerResult = await businessLogic.tryAnswerWithToolResult(
      body.conversation_id,
      executionId,
      body.original_message,
      {
        toolName: body.tool_name,
        result: toolResult,
      },
      context,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Tool call executed successfully",
        toolName: body.tool_name,
        toolResult,
        tryAnswerResult,
      }),
    }
  } catch (error) {
    console.error("❌ Error during tool call execution:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error during tool execution"],
        ["TOOL_EXECUTION_ERROR"],
      ),
    }
  }
}

// Helper function to validate tool parameters
function validateToolParameters(
  providedParams: Record<string, any>,
  expectedParams: Array<{
    name: string
    type: string
    required: boolean
    description: string
  }>,
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check required parameters
  for (const param of expectedParams) {
    if (param.required && !(param.name in providedParams)) {
      errors.push(`Required parameter '${param.name}' is missing`)
    }
  }

  // Check parameter types (basic validation)
  for (const [paramName, paramValue] of Object.entries(providedParams)) {
    const expectedParam = expectedParams.find(p => p.name === paramName)
    if (!expectedParam) {
      errors.push(`Unexpected parameter '${paramName}'`)
      continue
    }

    // Basic type validation
    const actualType = typeof paramValue
    const expectedType = expectedParam.type

    if (expectedType === "string" && actualType !== "string") {
      errors.push(`Parameter '${paramName}' should be string, got ${actualType}`)
    } else if (expectedType === "number" && actualType !== "number") {
      errors.push(`Parameter '${paramName}' should be number, got ${actualType}`)
    } else if (expectedType === "boolean" && actualType !== "boolean") {
      errors.push(`Parameter '${paramName}' should be boolean, got ${actualType}`)
    } else if (expectedType === "object" && (actualType !== "object" || Array.isArray(paramValue))) {
      errors.push(`Parameter '${paramName}' should be object, got ${actualType}`)
    } else if (expectedType === "array" && !Array.isArray(paramValue)) {
      errors.push(`Parameter '${paramName}' should be array, got ${actualType}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Mock tool execution function (replace with actual implementation later)
async function mockToolExecution(
  toolName: string,
  parameters: Record<string, any>,
  toolConfig: {
    toolName: string
    description: string
    parameters: Array<any>
  },
): Promise<{ success: boolean; data?: any; error?: string }> {
  console.log(`🔧 Executing mock tool: ${toolName}`)
  console.log(`📝 Tool description: ${toolConfig.description}`)
  console.log(`📋 Parameters:`, parameters)

  // Simulate some processing time
  await new Promise(resolve => setTimeout(resolve, 100))

  // Mock different tool responses based on tool name
  switch (toolName) {
    case "get_weather":
      return {
        success: true,
        data: {
          location: parameters.location || "Unknown",
          temperature: "22°C",
          condition: "Sunny",
          humidity: "65%",
          forecast: parameters.include_forecast ? "7-day forecast available" : undefined,
        },
      }

    case "search_database":
      return {
        success: true,
        data: {
          query: parameters.query || "",
          results: [
            { id: 1, title: "Sample Result 1", description: "Mock search result" },
            { id: 2, title: "Sample Result 2", description: "Another mock result" },
          ],
          total: 2,
        },
      }

    case "calculate":
      return {
        success: true,
        data: {
          expression: parameters.expression || "",
          result: "42", // Mock calculation result
        },
      }

    default:
      // Generic success response for unknown tools
      return {
        success: true,
        data: {
          message: `Tool ${toolName} executed successfully`,
          parameters,
          timestamp: new Date().toISOString(),
        },
      }
  }
}
