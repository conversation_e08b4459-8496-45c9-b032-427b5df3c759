import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import { getExecutionIdContext } from "../../functions/webhook/impl"
import { implHandleReply, implToolCall } from "./impl"
import { getAiServices } from "./shared"

export async function POST(req: NextRequest) {
  const { aiBusinessLogic } = getAiServices()
  try {
    const body = await req.json()

    const context = await getExecutionIdContext(body.execution_id)
    if (!context) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Workflow execution not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    if (!body.context) {
      return NextResponse.json(
        {
          status: "failed",
          errors: ["context field is required"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED],
        },
        { status: 400 },
      )
    }

    let result
    const ctx = body.context.toUpperCase()
    const sessionId = body.session_id
    const executionId = body.execution_id

    switch (ctx) {
      case "REPLY":
        result = await implHandleReply(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
          context,
        )
        break
      case "TOOL_CALLING":
        result = await implToolCall(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
          context,
        )
        break
      default:
        return NextResponse.json(
          {
            status: "failed",
            errors: [`Unsupported context: ${body.context}`],
            errorCodes: [ERROR_CODES.VALIDATION_FAILED],
          },
          { status: 400 },
        )
    }

    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AI POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
