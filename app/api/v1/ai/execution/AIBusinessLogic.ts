import { IDatasourceRepository } from "./repository-interface"
import { AIInferenceEngine } from "./AIInferenceEngine"
import { AiRule, AiRuleBusinessLogic } from "@/lib/repositories/aiRules"
import { MessageTemplate, MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates"
import { SessionContext } from "@/lib/repositories/auth/types"
import { KnowledgeBaseBusinessLogic } from "@/lib/repositories/knowledgeBase/BusinessLogic"
import { Datasource, DatasourceBusinessLogic } from "@/lib/repositories/datasources"
import { KnowledgeBaseParsedEntry } from "@/lib/repositories/knowledgeBase/interface"

export class AiBusinessLogic {
  constructor(
    private runner: AIInferenceEngine,
    private aiRuleBusinessLogic: AiRuleBusinessLogic,
    private messageTemplatesBusinessLogic: MessageTemplateBusinessLogic,
    private datasourceRepository: IDatasourceRepository,
    private dataSourceBusinessLogic: DatasourceBusinessLogic,
    private knowledgeBaseBusinessLogic: KnowledgeBaseBusinessLogic,
  ) { }

  async tryAnswer(
    conversationId: string,
    executionId: string,
    message: string,
    context: SessionContext,
  ) {
    const PREVIOUS_MESSAGES = await this.datasourceRepository.getLastMessages(
      conversationId,
      30,
      context,
    )

    const { rules, templates, datasource, knowledgeBase } = await this.getContextualData(message, context)

    const RULES = this.mapRules(rules)
    const TEMPLATES = this.mapMessageTemplates(templates)

    const RAGS = [...this.mapTextDatasources(datasource), ...this.mapKnowledgeBase(knowledgeBase)]
    const TOOLS = this.mapToolDatasources(datasource)

    await this.runner.execute(conversationId, executionId, "TRY_ANSWER", {
      message,
      PREVIOUS_MESSAGES,
      data: { RULES, TEMPLATES, RAGS, TOOLS },
    })

    return { RULES, TEMPLATES, RAGS, TOOLS, PREVIOUS_MESSAGES }
  }

  async tryAnswerWithToolResult(
    conversationId: string,
    executionId: string,
    originalMessage: string,
    toolResult: {
      toolName: string
      result: { success: boolean; data?: any; error?: string }
    },
    context: SessionContext,
  ) {
    const PREVIOUS_MESSAGES = await this.datasourceRepository.getLastMessages(
      conversationId,
      30,
      context,
    )

    const { rules, templates, datasource, knowledgeBase } = await this.getContextualData(originalMessage, context)

    const RULES = this.mapRules(rules)
    const TEMPLATES = this.mapMessageTemplates(templates)

    const RAGS = [...this.mapTextDatasources(datasource), ...this.mapKnowledgeBase(knowledgeBase, true)]

    const TOOL_RESULT = {
      toolName: toolResult.toolName,
      success: toolResult.result.success,
      data: toolResult.result.data,
      error: toolResult.result.error,
    }

    await this.runner.execute(conversationId, executionId, "TRY_ANSWER", {
      message: originalMessage,
      PREVIOUS_MESSAGES,
      data: { RULES, TEMPLATES, RAGS, TOOL_RESULT },
    })

    return { RULES, TEMPLATES, RAGS, PREVIOUS_MESSAGES, TOOL_RESULT }
  }

  private async getContextualData(message: string, context: SessionContext) {
    const [rulesResult, templatesResult, datasourceResult, knowledgeBaseResult] = await Promise.allSettled([
      this.aiRuleBusinessLogic.searchMatchingQuery(message, context, 3),
      this.messageTemplatesBusinessLogic.searchMatchingQuery(message, context, 3),
      this.dataSourceBusinessLogic.searchMatchingQuery(message, context, 3),
      this.knowledgeBaseBusinessLogic.searchMatchinQuery(message, context, 3),
    ])

    const result = {
      rules: rulesResult.status === "fulfilled" ? rulesResult.value : [],
      templates: templatesResult.status === "fulfilled" ? templatesResult.value : [],
      datasource: datasourceResult.status === "fulfilled" ? datasourceResult.value : [],
      knowledgeBase: knowledgeBaseResult.status === "fulfilled" ? knowledgeBaseResult.value : [],
    }

    return result
  }

  private mapRules(rules: AiRule[]) {
    return rules
      .map((r) => ({
        id: r.id,
        name: r.name,
        description: r.description,
        conditions: r.conditions,
        actions: r.actions,
        tags: r.tags,
      }))
  }

  private mapMessageTemplates(templates: MessageTemplate[]) {
    return templates
      .map((t) => ({
        id: t.id,
        title: t.title,
        query: t.query,
        template: t.template,
        tags: t.tags,
        variables: t.variables,
      }))
  }

  private mapTextDatasources(datasource: Datasource[]) {
    return datasource
      .filter((d) => d.type === "TEXT")
      .map((d) => ({
        id: d.id,
        name: d.name,
        content: d.content,
        type: d.type,
      }))
  }

  private mapToolDatasources(datasource: Datasource[]) {
    return datasource
      .filter((d) => d.type === "TOOL")
      .map((d) => ({
        id: d.id,
        name: d.name,
        toolCallConfig: d.toolCallConfig,
      }))
  }

  // 🔽 Filter knowledge base entries
  private mapKnowledgeBase(knowledgeBase: KnowledgeBaseParsedEntry[], verbose = false) {
    return knowledgeBase
      .map((kb) => verbose
        ? {
          id: kb.id,
          title: kb.title,
          content: kb.content,
          keywords: kb.keywords || [],
          category: kb.category,
          tags: kb.tags || [],
        }
        : {
          id: kb.id,
          title: kb.title,
          content: kb.content,
        }
      )
  }
}
