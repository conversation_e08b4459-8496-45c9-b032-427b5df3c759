import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  BroadcastBusinessLogicInterface,
  BroadcastRecipientBusinessLogicInterface,
} from "@/lib/repositories/broadcast/interface"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ERROR_CODES } from "@/app/api/error_codes"
import {
  BroadcastCreateSchema,
  BroadcastUpdateSchema,
  BroadcastQuerySchema,
} from "@/lib/validations/broadcast"
import {
  Broadcast,
  BroadcastRecipient,
  BroadcastStats,
} from "@/lib/repositories/broadcast/interface"
import { GetAllResultPaginated } from "../ai-workflow-executions/impl"

// Create Broadcast Implementation
export async function implHandleCreateBroadcast(
  data: any,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validationResult = BroadcastCreateSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Add context fields to the validated data
    const broadcastData = {
      ...validationResult.data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const broadcast = await businessLogic.create(broadcastData, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", broadcast),
    }
  } catch (error: any) {
    console.error("Error creating broadcast:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to create broadcast"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Update Broadcast Implementation
export async function implHandleUpdateBroadcast(
  id: string,
  data: any,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate ID
    if (!id || typeof id !== "string" || id.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Broadcast ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const validationResult = BroadcastUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const broadcast = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!broadcast) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Broadcast not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", broadcast, [
        "Broadcast updated successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error updating broadcast:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to update broadcast"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Delete Broadcast Implementation
export async function implHandleDeleteBroadcast(
  id: string,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
  hardDelete = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Broadcast not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { deleted: true }, [
        hardDelete ? "Broadcast permanently deleted" : "Broadcast deleted",
      ]),
    }
  } catch (error: any) {
    console.error("Error deleting broadcast:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to delete broadcast"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get Broadcast by ID Implementation
export async function implHandleGetBroadcastById(
  id: string,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
  includeDeleted = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const broadcast = await businessLogic.getById(id, context, includeDeleted)

    if (!broadcast) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Broadcast not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", broadcast),
    }
  } catch (error: any) {
    console.error("Error retrieving broadcast:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve broadcast"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get All Broadcasts Implementation
export async function implHandleGetAllBroadcasts(
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    status?: string
    createdBy?: string
    dateFrom?: string
    dateTo?: string
    sortBy?: string
    sortOrder?: string
  },
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validationResult = BroadcastQuerySchema.safeParse(params)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const queryParams = {
      search: validationResult.data.search,
      page: validationResult.data.page,
      limit: validationResult.data.limit,
      includeDeleted: validationResult.data.includeDeleted,
      filters: [
        ...(validationResult.data.status
          ? [{ field: "status", value: validationResult.data.status }]
          : []),
        ...(validationResult.data.createdBy
          ? [{ field: "createdBy", value: validationResult.data.createdBy }]
          : []),
        ...(validationResult.data.dateFrom
          ? [
              {
                field: "createdAt",
                value: { $gte: new Date(validationResult.data.dateFrom) },
              },
            ]
          : []),
        ...(validationResult.data.dateTo
          ? [
              {
                field: "createdAt",
                value: { $lte: new Date(validationResult.data.dateTo) },
              },
            ]
          : []),
      ],
      sorts: [
        {
          field: validationResult.data.sortBy,
          direction: validationResult.data.sortOrder as "asc" | "DESC",
        },
      ],
    }

    const result = await businessLogic.getAll(queryParams, context)

    const response: GetAllResultPaginated<Broadcast> = {
      items: result.items,
      total: result.total,
      page: validationResult.data.page,
      // limit: validationResult.data.limit,
      // totalPages: Math.ceil(result.total / validationResult.data.limit),
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", response),
    }
  } catch (error: any) {
    console.error("Error retrieving broadcasts:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve broadcasts"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Start Broadcast Implementation
export async function implHandleStartBroadcast(
  id: string,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.startBroadcast(id, context)

    return {
      status: 200,
      body: new ResponseWrapper("success", { success }),
    }
  } catch (error: any) {
    console.error("Error starting broadcast:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to start broadcast"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Cancel Broadcast Implementation
export async function implHandleCancelBroadcast(
  id: string,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.cancelBroadcast(id, context)

    return {
      status: 200,
      body: new ResponseWrapper("success", { success }, [
        "Broadcast cancelled successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error cancelling broadcast:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to cancel broadcast"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get Broadcast Progress Implementation
export async function implHandleGetBroadcastProgress(
  id: string,
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const progress = await businessLogic.getBroadcastProgress(id, context)

    return {
      status: 200,
      body: new ResponseWrapper("success", progress),
    }
  } catch (error: any) {
    console.error("Error retrieving broadcast progress:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve broadcast progress"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get Broadcast Recipients Implementation
export async function implHandleGetBroadcastRecipients(
  broadcastId: string,
  businessLogic: BroadcastBusinessLogicInterface,
  broadcastRecipientBusinessLogic: BroadcastRecipientBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const recipients = await broadcastRecipientBusinessLogic.getByBroadcastId(
      broadcastId,
      context,
    )

    const response: GetAllResultPaginated<BroadcastRecipient> = {
      items: recipients,
      total: recipients.length,
      page: 1,
      // limit: validationResult.data.limit,
      // totalPages: Math.ceil(result.total / validationResult.data.limit),
    }
    return {
      status: 200,
      body: new ResponseWrapper("success", response),
    }
  } catch (error: any) {
    console.error("Error retrieving broadcast recipients:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve broadcast recipients"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get Broadcast Stats Implementation
export async function implHandleGetBroadcastStats(
  businessLogic: BroadcastBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const stats = await businessLogic.getStats(context)

    return {
      status: 200,
      body: new ResponseWrapper("success", stats, [
        "Broadcast stats retrieved successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error retrieving broadcast stats:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve broadcast stats"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
